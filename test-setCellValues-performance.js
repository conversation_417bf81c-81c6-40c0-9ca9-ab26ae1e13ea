/**
 * 性能测试脚本 - 测试优化后的 setCellValues 方法
 * 
 * 这个脚本用于验证 setCellValues 方法的性能优化效果
 */

// 模拟测试数据生成器
function generateTestData(rowCount, columnCount) {
  const cellUpdates = [];
  
  for (let col = 0; col < columnCount; col++) {
    for (let row = 0; row < rowCount; row++) {
      cellUpdates.push({
        row: row,
        column: col,
        value: `测试数据_${row}_${col}`,
        options: {}
      });
    }
  }
  
  return cellUpdates;
}

// 生成连续行数据（用于测试范围优化）
function generateConsecutiveTestData(startRow, endRow, column) {
  const cellUpdates = [];
  
  for (let row = startRow; row <= endRow; row++) {
    cellUpdates.push({
      row: row,
      column: column,
      value: `连续数据_${row}`,
      options: {}
    });
  }
  
  return cellUpdates;
}

// 生成非连续行数据（测试单个单元格处理）
function generateNonConsecutiveTestData(rows, column) {
  const cellUpdates = [];
  
  rows.forEach(row => {
    cellUpdates.push({
      row: row,
      column: column,
      value: `非连续数据_${row}`,
      options: {}
    });
  });
  
  return cellUpdates;
}

// 生成带自定义选项的数据（测试混合处理）
function generateMixedTestData(rowCount, column) {
  const cellUpdates = [];
  
  for (let row = 0; row < rowCount; row++) {
    const hasCustom = row % 3 === 0; // 每3行有一个自定义选项
    
    cellUpdates.push({
      row: row,
      column: column,
      value: `混合数据_${row}`,
      options: hasCustom ? { custom: { testFlag: true } } : {}
    });
  }
  
  return cellUpdates;
}

// 性能测试函数
async function performanceTest(testName, cellUpdates, jobTableInstance) {
  console.log(`\n=== ${testName} ===`);
  console.log(`测试数据量: ${cellUpdates.length} 个单元格`);
  
  const startTime = performance.now();
  
  try {
    const result = await jobTableInstance.setCellValues(cellUpdates);
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`执行结果: ${result ? '成功' : '失败'}`);
    console.log(`执行时间: ${duration.toFixed(2)} ms`);
    console.log(`平均每个单元格: ${(duration / cellUpdates.length).toFixed(4)} ms`);
    
    return {
      success: result,
      duration: duration,
      avgPerCell: duration / cellUpdates.length
    };
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.error(`执行失败: ${error.message}`);
    console.log(`失败时间: ${duration.toFixed(2)} ms`);
    
    return {
      success: false,
      duration: duration,
      error: error.message
    };
  }
}

// 主测试函数
async function runPerformanceTests(jobTableInstance) {
  console.log('开始 setCellValues 性能测试...');
  
  const results = [];
  
  // 测试1: 小量数据 (10个单元格)
  const smallData = generateTestData(10, 1);
  results.push(await performanceTest('小量数据测试', smallData, jobTableInstance));
  
  // 测试2: 中等数据 (100个单元格)
  const mediumData = generateTestData(100, 1);
  results.push(await performanceTest('中等数据测试', mediumData, jobTableInstance));
  
  // 测试3: 大量数据 (1000个单元格)
  const largeData = generateTestData(1000, 1);
  results.push(await performanceTest('大量数据测试', largeData, jobTableInstance));
  
  // 测试4: 连续行数据 (测试范围优化)
  const consecutiveData = generateConsecutiveTestData(0, 99, 0);
  results.push(await performanceTest('连续行数据测试', consecutiveData, jobTableInstance));
  
  // 测试5: 非连续行数据
  const nonConsecutiveRows = [0, 2, 4, 6, 8, 10, 15, 20, 25, 30];
  const nonConsecutiveData = generateNonConsecutiveTestData(nonConsecutiveRows, 0);
  results.push(await performanceTest('非连续行数据测试', nonConsecutiveData, jobTableInstance));
  
  // 测试6: 混合数据（有些带自定义选项）
  const mixedData = generateMixedTestData(50, 0);
  results.push(await performanceTest('混合数据测试', mixedData, jobTableInstance));
  
  // 测试7: 多列数据
  const multiColumnData = generateTestData(50, 3);
  results.push(await performanceTest('多列数据测试', multiColumnData, jobTableInstance));
  
  // 输出总结
  console.log('\n=== 性能测试总结 ===');
  results.forEach((result, index) => {
    const testNames = [
      '小量数据测试',
      '中等数据测试', 
      '大量数据测试',
      '连续行数据测试',
      '非连续行数据测试',
      '混合数据测试',
      '多列数据测试'
    ];
    
    console.log(`${testNames[index]}: ${result.success ? '✓' : '✗'} ${result.duration.toFixed(2)}ms (${result.avgPerCell.toFixed(4)}ms/cell)`);
  });
  
  return results;
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateTestData,
    generateConsecutiveTestData,
    generateNonConsecutiveTestData,
    generateMixedTestData,
    performanceTest,
    runPerformanceTests
  };
}

// 如果在浏览器环境中，将函数添加到全局对象
if (typeof window !== 'undefined') {
  window.setCellValuesPerformanceTest = {
    generateTestData,
    generateConsecutiveTestData,
    generateNonConsecutiveTestData,
    generateMixedTestData,
    performanceTest,
    runPerformanceTests
  };
}
